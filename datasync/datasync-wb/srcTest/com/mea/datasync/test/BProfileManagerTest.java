// In: com.mea.datasync.test
package com.mea.datasync.test;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;

import org.testng.Assert;
import org.testng.annotations.Test;

import com.mea.datasync.model.BConnectionProfile;
import com.mea.datasync.persistence.ProfileManager;
import javax.baja.test.BTestNg;

/**
 * BProfileManagerTest tests the ProfileManager functionality for
 * saving and loading connection profiles to/from JSON files.
 */
@NiagaraType
public class BProfileManagerTest extends BTestNg {

//region /*+ ------------ B<PERSON>IN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.test.BProfileManagerTest(2979906276)1.0$ @*/
/* Generated by Slot-o-Matic (c) Tridium, Inc. 2012-2024 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BProfileManagerTest.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/

////////////////////////////////////////////////////////////////
// Test Methods
////////////////////////////////////////////////////////////////

  /**
   * Test basic ProfileManager functionality - creating, saving, and loading profiles.
   */
  @Test(description = "Test ProfileManager save and load functionality")
  public void testProfileManagerSaveLoad() {
    try {
      System.out.println("Starting ProfileManager save/load test");
      
      // Create ProfileManager instance
      ProfileManager profileManager = new ProfileManager();
      Assert.assertNotNull(profileManager, "ProfileManager should be created");
      
      // Create a test profile
      BConnectionProfile testProfile = new BConnectionProfile();
      testProfile.setSourceType("Excel");
      testProfile.setSourcePath("C:\\Test\\test.xlsx");
      testProfile.setSheetName("TestSheet");
      testProfile.setTargetHost("localhost");
      testProfile.setTargetPath("station:|slot:/Test");
      testProfile.setStatus("Test");
      testProfile.setComponentsCreated(42);
      
      // Verify properties are set correctly
      Assert.assertEquals(testProfile.getSourceType(), "Excel", "Source type should be set");
      Assert.assertEquals(testProfile.getSourcePath(), "C:\\Test\\test.xlsx", "Source path should be set");
      Assert.assertEquals(testProfile.getSheetName(), "TestSheet", "Sheet name should be set");
      Assert.assertEquals(testProfile.getTargetHost(), "localhost", "Target host should be set");
      Assert.assertEquals(testProfile.getTargetPath(), "station:|slot:/Test", "Target path should be set");
      Assert.assertEquals(testProfile.getStatus(), "Test", "Status should be set");
      Assert.assertEquals(testProfile.getComponentsCreated(), 42, "Components created should be set");
      
      // Save the profile
      String profileName = "TestProfile";
      boolean saveResult = profileManager.saveProfile(testProfile, profileName);
      Assert.assertTrue(saveResult, "Profile should be saved successfully");
      
      // Verify profile exists
      boolean exists = profileManager.profileExists(profileName);
      Assert.assertTrue(exists, "Profile should exist after saving");
      
      // Load the profile back
      BConnectionProfile loadedProfile = profileManager.loadProfile(profileName);
      Assert.assertNotNull(loadedProfile, "Loaded profile should not be null");
      
      // Verify loaded profile has correct data
      Assert.assertEquals(loadedProfile.getSourceType(), "Excel", "Loaded source type should match");
      Assert.assertEquals(loadedProfile.getSourcePath(), "C:\\Test\\test.xlsx", "Loaded source path should match");
      Assert.assertEquals(loadedProfile.getSheetName(), "TestSheet", "Loaded sheet name should match");
      Assert.assertEquals(loadedProfile.getTargetHost(), "localhost", "Loaded target host should match");
      Assert.assertEquals(loadedProfile.getTargetPath(), "station:|slot:/Test", "Loaded target path should match");
      Assert.assertEquals(loadedProfile.getStatus(), "Test", "Loaded status should match");
      Assert.assertEquals(loadedProfile.getComponentsCreated(), 42, "Loaded components created should match");
      
      // Clean up - delete the test profile
      boolean deleteResult = profileManager.deleteProfile(profileName);
      Assert.assertTrue(deleteResult, "Profile should be deleted successfully");
      
      // Verify profile no longer exists
      boolean existsAfterDelete = profileManager.profileExists(profileName);
      Assert.assertFalse(existsAfterDelete, "Profile should not exist after deletion");
      
      System.out.println("ProfileManager save/load test completed successfully");
      
    } catch (Exception e) {
      System.err.println("Test failed with exception: " + e.getMessage());
      e.printStackTrace();
      Assert.fail("Test failed with exception: " + e.getMessage());
    }
  }

  /**
   * Test ProfileManager directory creation and file listing.
   */
  @Test(description = "Test ProfileManager directory and file operations")
  public void testProfileManagerDirectoryOperations() {
    try {
      System.out.println("Starting ProfileManager directory operations test");
      
      // Create ProfileManager instance
      ProfileManager profileManager = new ProfileManager();
      
      // Get profiles directory
      java.io.File profilesDir = profileManager.getProfilesDirectory();
      Assert.assertNotNull(profilesDir, "Profiles directory should not be null");
      Assert.assertTrue(profilesDir.exists(), "Profiles directory should exist");
      Assert.assertTrue(profilesDir.isDirectory(), "Profiles directory should be a directory");
      
      // Test listing profiles (should work even if empty)
      java.util.List<String> profiles = profileManager.listProfiles();
      Assert.assertNotNull(profiles, "Profile list should not be null");
      
      System.out.println("Found " + profiles.size() + " existing profiles");
      for (String profile : profiles) {
        System.out.println("  - " + profile);
      }
      
      System.out.println("ProfileManager directory operations test completed successfully");
      
    } catch (Exception e) {
      System.err.println("Test failed with exception: " + e.getMessage());
      e.printStackTrace();
      Assert.fail("Test failed with exception: " + e.getMessage());
    }
  }
}
