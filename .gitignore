# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Exclude specific PDF files
DD2-Niagara\ Development\ with\ Gradle.pdf
**/DD2-Niagara\ Development\ with\ Gradle.pdf
docDeveloper.pdf
**/docDeveloper.pdf

# docDeveloper documentation folder
docDeveloper-doc/

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# Gradle
.gradle/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# Gradle Wrapper
!gradle-wrapper.properties

# IDE
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Windsurf IDE
.windsurfrules

# Eclipse workspace metadata
.metadata/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Niagara specific
*.bog
*.backup
*.bak
*.tmp
*.temp

# Niagara source code (extracted for reference only)
niagara_source_code/

# Local environment variables
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Security and Certificate Files
# Private keys, certificates, and signing materials should never be committed
*.pem
*.p12
*.pfx
*.jks
*.jceks
*.keystore
*.truststore
security/
certificates/
keys/
*.key
*.crt
*.cer
*.der

# Niagara signing configuration (may contain sensitive paths/info)
niagara.signing.xml
**/niagara.signing.xml

# Any files containing passwords, tokens, or API keys
*password*
*secret*
*token*
*api-key*
*apikey*
config.properties
application-local.properties
application-dev.properties
