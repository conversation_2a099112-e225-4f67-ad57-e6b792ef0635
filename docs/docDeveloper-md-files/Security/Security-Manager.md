# Security Manager

## Overview

One of the changes implemented in Niagara 4 is the activation of the Java Security Manager. The Security Manager allows us to restrict who can call what code using permissions. By default, no one has any permissions. Any code that requires a permission check will fail, with an `AccessControllerException`. Each permission must be granted explicitly, using a policy file.

This allows us to ensure that certain sensitive calls can only be made by trusted code or individuals – for example, we can limit who can read, write, delete or execute specific files or folders, using a `java.io.FilePermission`. This way, we can protect sensitive files like the contents of the security folder, ensuring that only modules that absolutely need to access those files have permissions to do so.

As a developer, this means that you may encounter access control issues and defects, caused by the new Security Manager. In this document, we will show how to deal with these issues by:

-   Identifying Access Control Issues
-   Disabling the Security Manager
-   Reporting Security Manager Issues

## Identifying Access Control Issues

When using Niagara 4 with the Security Manager enabled, you may come across issues where certain features are no longer functioning as expected. These problems may or may not be related to the Security Manager. To identify whether it truly is an access control issue, you have a number of options. We will go over each of these options in the sections below.

### Inspect Output and Stack Traces

If you run across an issue you think may be related to the Security Manager, the first thing to do, as for any other issue, is to inspect the output for either the station or workbench, as appropriate. Typically, issues caused by the Security Manager will include one of the following in the output or stack trace:

-   `AccessControlException: access denied (<required permission>)`
-   `access denied (<required permission>)`

The presence of either of these two lines indicates a Security Manager issue, which should be reported.

### Enable Security Manager Debug Output

Not all issues (Security Manager related or otherwise) generate a stack trace or other output, making it a little trickier to determine if the issue you are seeing is related to the Security Manager or not. In these cases, there are logs that may be turned on to obtain additional debug information.

#### Niagara Debug Output

For some basic debug output, you can go to the `DebugService` in the station, or to `Tools > Logger Configuration` in workbench, and turn on the “`security.niagaraPolicy`” log. Different settings will give different levels of detail:

-   **FINE**: Logs failed permission checks, including the permission name and code base that failed the check.
-   **FINER**: Logs successful permission checks for certain Niagara-specific permissions, including the permission name and the Niagara permission that granted it.
-   **FINEST**: Logs successful permission checks for all permissions.

This is very basic information and is good for quickly determining the source of the problem in simple situations. For more complex situations, the built-in Java debug output can be used.

#### Built-in Java Debug Output

If you can start your station or workbench from the command line, Java offers a command line property to enable debugging on the Security Manager, allowing you to precisely identify access control issues. To enable Security Manager debug output, add the following (shown in green) to the command line:

`station <stationname> -@Djava.security.debug=access,failure`

Note that this will produce a LOT of output, which may be difficult to view from the console. If you would like to stream this output to a file, use the following (show in green):

`station <stationname> -@Djava.security.debug=access,failure > D:\tmp\debug.txt 2>&1`

where `D:\tmp\debug.txt` is the file you want to stream your output to. The path and file can be changed, and the path must exist.

**Note**

The full debug output may only completely stream to the file once the application stops. The best way to test Security Manager issues, if possible is to start your application, attempt to reproduce the issue as soon as possible, then close the program and inspect the debug output.

**Note**

The Security Manager debug feature produces a lot of output, a lot of which you don’t need to worry about if you’re looking for access denied issues. You can filter out a lot of output if you use a text editor which allows a search and replace based on regular expressions, such as Notepad++. Simply use find and replace with:

-   **Find**: `".*access allowed.*\r\n"`
-   **Replace with**: `""`

### Disable the Security Manager and Try Again

If you have an issue and don’t see any debug output, and cannot start your station or workbench from the command line, you do have another option available. If you know you can reproduce the issue consistently with the Security Manager on, you can stop the application and then disable the Security Manager, then restart the application. If you’re still seeing the issue, it’s not related to the Security Manager. If you aren’t seeing it anymore, it may be Security Manager related. See the following section for instructions on how to disable the Security Manager.

## Disabling the Security Manager

Enabling the Security Manager was a change with a massive scope. The Security Manager can potentially affect any and all features. As a result, it was not possible to identify every single potential issue before enabling it. In order to allow work to continue even when issues come up, we have provided the ability to disable the Security Manager when a blocking issue is found.

The first requirement to disable the security manager is to have the “`smDeveloperMode`” license feature. If you don’t have this license feature, you will not be able to disable the Security Manager even if you follow one of the methods described below.

Once you have the “`smDeveloperMode`” license feature, you need to request to your application that it run without the Security Manager. There are three ways to do this, which we will go over in the sections below.

### Use the Command Line Argument

If you can start your application using the command line, the simplest way to request to disable the Security Manager is using the command line argument. Simply add the following (shown in green) to the command line:

`station <stationname> -@Dniagara.security.manager.disable`

### Set a System Property

If you can’t start your application from the command line, you can request to disable the Security Manager by setting the `niagara.security.manager.disable` system property. The system property needs to be present at boot, so you should include it in your `<niagara.user.home>/etc/system.properties` file.

### Enable the no-security-manager Flag (QNX)

In QNX, if you can’t start your application from the command line, you can request to disable the Security Manager by opening an SSH connection to the JACE and issuing the following command:

`touch /etc/no-security-manager`

This will disable the Security Manager for both niagarad and stations.

## Reporting Security Manager Issues

Security Manager issues should be reported the same way as any other issue you come across. To help get the issue resolved more quickly, however, there are some additional details you can include.

When describing how the issue occurs, precise reproduction steps are extremely important with Security Manager issues. The order of operations can affect whether the issue occurs at all, as can any change from default settings, or the presence of a new Service, etc…

If you used the Security Manager command line debug argument, please include the entire debug output captured (before or after filtering the “access allowed” lines) in your issue report.

If the issue caused a stack trace or other output in the console, please include the entire stack trace in your issue report.

Having all this information from the start will allow issues to be dealt with much more quickly, and will reduce the need for follow ups.

## Fixing Access Control Issues

Currently, the ability for third party developers to fix access control issues is limited. We do, however, allow the following:

As (read/write/delete/execute) access to files is a restricted action requiring a specific permission for the file in question, third party modules that try to access the file system may run into issues. To address this, we have added shared directories that all modules have read/write/delete access to in the following locations:

-   Under the station. This location is now called `station_home`, and can be obtained via `Sys.getStationHome()`.
-   Under `niagara_user_home`. This location can be obtained via `Sys.getNiagaraSharedUserHome()`.
