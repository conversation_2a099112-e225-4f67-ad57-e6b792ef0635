<!-- Module Include File -->

<!-- Types -->
<types>
  <!-- Type Example:
  <type name="MyClass" class="com.acme.BMyClass"/>
  -->
  
  <!-- com.examples.envctrldriver -->
  <type name="DeviceDiscoveryJob" class="com.examples.envctrldriver.BDeviceDiscoveryJob"/>
  <type name="DeviceLearnEntry" class="com.examples.envctrldriver.BDeviceLearnEntry"/>  
  <type name="EnvCtrlDevice" class="com.examples.envctrldriver.BEnvCtrlDevice"/>
  <type name="EnvCtrlDeviceNetwork" class="com.examples.envctrldriver.BEnvCtrlDeviceNetwork"/>
  <type name="EnvCtrlDeviceFolder" class="com.examples.envctrldriver.BEnvCtrlDeviceFolder"/>
  <type name="EnvCtrlPollScheduler" class="com.examples.envctrldriver.BEnvCtrlPollScheduler"/>
  <type name="IEnvCtrlPollable" class="com.examples.envctrldriver.BIEnvCtrlPollable" />
  
  <!-- com.examples.envctrldriver.points -->
  <type name="EnvCtrlPointDeviceExt" class="com.examples.envctrldriver.points.BEnvCtrlPointDeviceExt"/>
  <type name="EnvCtrlPointFolder" class="com.examples.envctrldriver.points.BEnvCtrlPointFolder"/>
  <type name="EnvCtrlPointProxyExt" class="com.examples.envctrldriver.points.BEnvCtrlPointProxyExt"/>
  <type name="WriteWorker" class="com.examples.envctrldriver.points.BWriteWorker"/>
  <type name="PointDiscoveryJob" class="com.examples.envctrldriver.points.BPointDiscoveryJob"/>
  <type name="PointLearnEntry" class="com.examples.envctrldriver.points.BPointLearnEntry"/>

</types>