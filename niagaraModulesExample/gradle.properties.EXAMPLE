#Uncomment and set to the path of the Gradle plugins if they are not in niagara_home
#Rename this file to "gradle.properties"
#gradlePluginHome=
#The path to the installation of Niagara you are building against
niagara_home=
#The path to niagara_user_home for the version of Niagara you are building against
niagara_user_home=
#Uncomment and set to the path of your node install if you are building JavaScript modules
#nodeHome=
org.gradle.java.installations.auto-detect=false
org.gradle.java.installations.auto-download=false
#Set this to the location of the "JRE" folder in your niagara_home directory
org.gradle.java.installations.paths=
