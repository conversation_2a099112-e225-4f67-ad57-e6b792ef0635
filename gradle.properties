#Uncomment and set to the path of the Gradle plugins if they are not in niagara_home
#gradlePluginHome=
#The path to the installation of Niagara you are building against
niagara_home=C:\\PATH_TO_YOUR_NIAGARA_INSTALLATION
#The path to niagara_user_home for the version of Niagara you are building against
niagara_user_home=C:\\PATH_TO_YOUR_NIAGARA_USER_HOME
#Uncomment and set to the path of your node install if you are building JavaScript modules
#nodeHome=
#AUTOMATICALLY GENERATED BY GRADLE -- DO NOT MODIFY
org.gradle.java.installations.auto-detect=false
#AUTOMATICALLY GENERATED BY GRADLE -- DO NOT MODIFY
org.gradle.java.installations.auto-download=false
#AUTOMATICALLY GENERATED BY GRADLE -- DO NOT MODIFY
org.gradle.java.installations.paths=C:\\Honeywell\\OptimizerSupervisor-N4.13.3.48\\jre
